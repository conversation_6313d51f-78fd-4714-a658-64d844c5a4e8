@page "/edit-users"
@using Microsoft.AspNetCore.Authorization
@using Shared
@inject HttpClient Http
@inject NavigationManager Navigation

@inherits Client.Base.AuthenticatedComponentBase
@attribute [Authorize(Roles = "admin")]

<h3>Edit Users</h3>
<MudChipSet T="UserGridView" SelectedValue="@SelectedGridView" SelectedValueChanged="OnSelectedGridViewChanged" SelectionMode="SelectionMode.SingleSelection">
    <MudChip Text="pink" 
             Variant="Variant.Text" 
             Color="Color.Secondary" 
             Default="true" 
             Value="UserGridView.All">All (@_foster_count)</MudChip>
    <MudChip Text="pink" 
             Variant="Variant.Text" 
             Color="@(_pending_count > 0 ? Color.Warning : Color.Secondary)" 
             Value="UserGridView.Pending">Pending (@_pending_count)</MudChip>
    <MudChip Text="pink" 
             Variant="Variant.Text" 
             Color="@(_no_team_leader_count > 0 ? Color.Warning : Color.Secondary)" 
             Value="UserGridView.NoTeamLeader">No Team Leader (@_no_team_leader_count)</MudChip>
</MudChipSet>
<MudDataGrid T="FosterDto" 
             Items="@_fosters" 
             MultiSelection="true" 
             SelectOnRowClick="false"  
             ReadOnly="false" 
             HorizontalScrollbar="true" 
             Dense="true" 
             @ref="_dataGrid" 
             EditMode="DataGridEditMode.Cell" 
             CommittedItemChanges="@CommittedItemChanges">
    <Columns>
        <PropertyColumn Title="First Name" 
                        Property="x => x.FirstName" 
                        StickyLeft="true"/>
        <PropertyColumn Title="Last Name" 
                        Property="x => x.LastName" 
                        StickyLeft="true"/>
        <TemplateColumn Title="Role">
            <EditTemplate>
                <MudSelect T="int" 
                           Value="@context.Item.RoleId" 
                           ValueChanged="x => HandleRoleValueChanged(context, x)" 
                           Dense="true" 
                           Variant="Variant.Outlined" 
                           Margin="@Margin.Dense" 
                           ToStringFunc="GetRoleDescription">
                    @foreach (var role in _roles)
                    {
                        <MudSelectItem T="int" Value="role.Id">@role.RoleDescription</MudSelectItem>
                    }
                </MudSelect>
            </EditTemplate>
        </TemplateColumn>
        <TemplateColumn Title="Is Team Leader?">
            <EditTemplate>
                <MudCheckBox T="bool" Value="@context.Item.IsTeamLeader" ValueChanged="x => HandleIsTeamLeaderValueChanged(context, x)"></MudCheckBox>
            </EditTemplate>
        </TemplateColumn>
        <TemplateColumn Title="Team Leader">
            <EditTemplate>
                <MudSelect T="int" 
                           Value="@(context.Item.TeamLeaderId ?? 0)" 
                           Disabled="@context.Item.IsTeamLeader" 
                           ValueChanged="x => HandleTeamLeaderValueChanged(context, x)" 
                           Dense="true" 
                           Variant="Variant.Outlined" 
                           Margin="@Margin.Dense" 
                           ToStringFunc="GetFosterName">
                    <MudSelectItem T="int" Value="0">No Team Leader</MudSelectItem>
                    @foreach (var teamLeader in _teamLeaders)
                    {
                        <MudSelectItem T="int" Value="teamLeader.Id">@teamLeader.FullName</MudSelectItem>
                    }
                </MudSelect>
            </EditTemplate>
        </TemplateColumn>
        <TemplateColumn Title="Team Leader Colour">
            <EditTemplate>
                <MudColorPicker  @bind-Text="_colorValue" Style="@($"color: {_colorValue};")" Placeholder="Select Color" />
            </EditTemplate>
        </TemplateColumn>
    </Columns>
</MudDataGrid>

@code 
{
    MudDataGrid<FosterDto>? _dataGrid;
    List<FosterDto> _fosters = [];
    List<FosterDto> _teamLeaders = [];
    List<Role> _roles = [];
    UserGridView SelectedGridView { get; set; }

    int _foster_count;
    int _pending_count;
    int _no_team_leader_count;
    
    string _colorValue = "#ffffff";
    
    protected override async Task OnInitializedAsync()
    {
        await GetData();
    }
    
    private async Task GetData()
    {
        try
        {
            _fosters = await HttpStuff.GetAllFosters(Http);
            _roles = await HttpStuff.GetRoles(Http);
            _teamLeaders = _fosters.Where(x => x.IsTeamLeader).ToList();
            
            // Count the number of fosters in each category
            _foster_count = _fosters.Count;
            _pending_count = _fosters.Count(x => x.RoleId == _roles.FirstOrDefault(r => r.RoleCode == "PENDING")?.Id);
            _no_team_leader_count = _fosters.Count(x => x.TeamLeaderId == null);
            
            // Set TeamLeader value if the foster has a team leader set
            foreach (var foster in _fosters)
            {
                if (foster.TeamLeaderId > 0)
                    foster.TeamLeaderFullName = _teamLeaders.First(x => x.Id == foster.TeamLeaderId).FullName;
            }
            
            // Filter fosters based on selected chip
            if (SelectedGridView == UserGridView.Pending)
            {
                var pendingRole = _roles.First(x => x.RoleCode == "PENDING");
                _fosters = _fosters.Where(x => x.RoleId == pendingRole.Id).ToList();
            }
            else if (SelectedGridView == UserGridView.NoTeamLeader)
            {
                _fosters = _fosters.Where(x => x.TeamLeaderId == null).ToList();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex.ToString());
        }
    }
    
    async Task OnSelectedGridViewChanged(UserGridView view)
    {
        SelectedGridView = view;
        await GetData();
    }
    
    string GetFosterName(int id)
    {
        return _fosters.FirstOrDefault(x => x.Id == id)?.FullName ?? string.Empty;
    }
    
    string GetRoleDescription(int id)
    {
        return _roles.First(x => x.Id == id).RoleDescription;
    }
    
    async Task HandleRoleValueChanged(CellContext<FosterDto> context, int newRoleId)
    {
        context.Item.RoleId = newRoleId;
        
        await _dataGrid!.CommittedItemChanges.InvokeAsync(context.Item);
    }
    
    async Task HandleIsTeamLeaderValueChanged(CellContext<FosterDto> context, bool newValue)
    {
        context.Item.IsTeamLeader = newValue;
        context.Item.TeamLeaderId = newValue ? context.Item.Id : null;
        
        await _dataGrid!.CommittedItemChanges.InvokeAsync(context.Item);
    }
    
    async Task HandleTeamLeaderValueChanged(CellContext<FosterDto> context, int newTeamLeaderId)
    {
        context.Item.TeamLeaderId = newTeamLeaderId == 0 ? null : newTeamLeaderId;
        await _dataGrid!.CommittedItemChanges.InvokeAsync(context.Item);
    }
    
    async Task CommittedItemChanges(FosterDto foster)
    {
        Console.WriteLine("Committed!");
        
        var response = await HttpStuff.UpdateFoster(Http, foster);

        await GetData();
    }
}